import numpy as np
from scipy import signal
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
import os
from scipy.fft import fft, fftfreq, fftshift

# Set seeds for reproducibility
np.random.seed(0)
torch.manual_seed(0)

# -----------------------------
# System parameters
# -----------------------------
fs = 128_000  # Sampling rate (Hz)
t = np.arange(128) / fs  # Time vector
R, L, C = 1e3, 1e-3, 10e-9  # Resistance (ohm), Inductance (H), Capacitance (F)

# -----------------------------
# Helper functions
# -----------------------------
def plot_convtranspose_weights(model):
    print("\nVisualizing ConvTranspose1d weights...")
    for name, module in model.named_modules():
        if isinstance(module, nn.ConvTranspose1d):
            weights = module.weight.detach().cpu().numpy()
            # weights shape: [out_channels, in_channels, kernel_size]
            weights = weights.squeeze()  # [9] if (1,1,9)
            plt.figure(figsize=(6, 3))
            plt.plot(weights, marker='o')
            plt.title(f"Weights of {name} (ConvTranspose1d)")
            plt.xlabel("Kernel Index")
            plt.ylabel("Weight Value")
            plt.grid(True)
            plt.tight_layout()
            plt.show()

def plot_convtranspose_frequency_response(model, fs=100_000):
    for name, module in model.named_modules():
        if isinstance(module, nn.ConvTranspose1d):
            weights = module.weight.detach().cpu().numpy().squeeze()  # [kernel_size]
            kernel_size = weights.shape[0]

            # FFT
            freq_axis = fftfreq(kernel_size, d=1/fs)  # in Hz
            freq_axis = fftshift(freq_axis)  # shift zero freq to center
            spectrum = np.abs(fftshift(fft(weights, n=kernel_size)))

            # Plot time-domain kernel
            plt.figure(figsize=(12, 4))
            plt.subplot(1, 2, 1)
            plt.plot(weights, marker='o')
            plt.title(f"{name} - Kernel (Time Domain)")
            plt.xlabel("Kernel Index")
            plt.ylabel("Weight")
            plt.grid(True)

            # Plot frequency-domain response
            plt.subplot(1, 2, 2)
            plt.plot(freq_axis / 1000, spectrum)
            plt.title(f"{name} - Frequency Response (Magnitude Spectrum)")
            plt.xlabel("Frequency (kHz)")
            plt.ylabel("Magnitude")
            plt.grid(True)

            plt.tight_layout()
            plt.show()

def rlc_response(v_in):
    """Simulate the RLC circuit response to an input voltage signal"""
    num = [1]
    den = [L * C, R * C, 1]
    system = signal.TransferFunction(num, den)
    _, y, _ = signal.lsim(system, U=v_in, T=t)
    return y

def generate_sine_wave(frequency, amplitude=2.0):
    return amplitude * np.sin(2 * np.pi * frequency * t)

def generate_square_wave(frequency, amplitude=2.0):
    return amplitude * signal.square(2 * np.pi * frequency * t, duty=0.45)

def generate_dataset(frequencies, wave_function, amplitudes):
    inputs, outputs = [], []
    for f in frequencies:
        for amp in amplitudes:
            x = wave_function(frequency=f, amplitude=amp)
            y = rlc_response(x)
            inputs.append(x)
            outputs.append(y)
    return np.array(inputs), np.array(outputs)

# -----------------------------
# Dataset generation
# -----------------------------
print("Generating training dataset...")
train_frequencies = np.arange(1000, 50001, 800)
train_amplitudes = np.arange(1.5, 2.51, 0.1)
train_inputs, train_outputs = generate_dataset(train_frequencies, generate_sine_wave, amplitudes=train_amplitudes)
#train_inputs, train_outputs = generate_dataset(train_frequencies, generate_square_wave, amplitudes=train_amplitudes)

print("Generating test dataset (sine + square)...")
test_frequencies = np.arange(1000, 50001, 200)
test_inputs, test_outputs, test_labels = [], [], []

for f in test_frequencies:
    sine_input = generate_sine_wave(f)
    square_input = generate_square_wave(f)
    test_inputs.append(sine_input)
    test_inputs.append(square_input)
    test_outputs.append(rlc_response(sine_input))
    test_outputs.append(rlc_response(square_input))
    test_labels.append(f"sine_{f}Hz")
    test_labels.append(f"square_{f}Hz")

test_inputs = np.array(test_inputs)
test_outputs = np.array(test_outputs)

# -----------------------------
# Neural network model
# -----------------------------
class RLCNet(nn.Module):
    def __init__(self):
        super().__init__()
        self.model = nn.Sequential(
            nn.Conv1d(1, 4, kernel_size=3, padding=1),
            nn.ReLU(),
            nn.Conv1d(4, 2, kernel_size=5, padding=2),
            nn.ReLU(),
            nn.Conv1d(2, 1, kernel_size=9, padding=4),
            #nn.ConvTranspose1d(1, 1, kernel_size=17, padding=8)
        )

    def forward(self, x):
        return self.model(x)

# -----------------------------
# Training function
# -----------------------------
def train_model(train_inputs, train_outputs, epochs=100, model_save_path="rlc_model.pth"):
    X_train = torch.tensor(train_inputs[:, None, :], dtype=torch.float32)
    y_train = torch.tensor(train_outputs[:, None, :], dtype=torch.float32)
    train_loader = DataLoader(TensorDataset(X_train, y_train), batch_size=16, shuffle=True)

    model = RLCNet()
    optimizer = torch.optim.Adam(model.parameters(), lr=1e-3)
    loss_f2 = nn.MSELoss()
    #loss_f1 = nn.L1Loss()
    
    best_train_loss = float('inf')
    best_model_state = None

    print("Training the model...")
    for epoch in range(epochs):
        model.train()
        train_loss_sum = 0.0
        train_batches = 0

        for xb, yb in train_loader:
            pred = model(xb)
            #loss = 0.5*(loss_f1(pred, yb)+loss_f1(pred, yb))
            loss = loss_f2(pred, yb)
            optimizer.zero_grad()
            loss.backward()
            optimizer.step()

            train_loss_sum += loss.item()
            train_batches += 1

        avg_train_loss = train_loss_sum / train_batches

        if avg_train_loss < best_train_loss:
            best_train_loss = avg_train_loss
            best_model_state = model.state_dict()
            #torch.save(best_model_state, model_save_path)
            print(f"Epoch {epoch:3d}: New best train loss = {avg_train_loss:.6f}, model saved.")

        if epoch % 10 == 0 or epoch == epochs - 1:
            print(f"Epoch {epoch:3d}: Avg Train loss = {avg_train_loss:.6f}")

    print(f"Training finished. Best train loss = {best_train_loss:.6f}")
    model.load_state_dict(best_model_state)
    return model

# -----------------------------
# Visualization function
# -----------------------------
def plot_predictions(inputs, true_outputs, predicted_outputs, labels, num=5):
    for i in range(0, len(inputs), len(inputs) // num):
        plt.figure(figsize=(10, 3))
        plt.plot(t, true_outputs[i], label="True Output", alpha=0.7)
        plt.plot(t, predicted_outputs[i], label="Predicted Output", linestyle='--')
        plt.title(f"Input Type: {labels[i]}")
        plt.xlabel("Time (s)")
        plt.ylabel("Voltage (V)")
        plt.legend()
        plt.tight_layout()
        plt.show()

# -----------------------------
# Main execution
# -----------------------------
if __name__ == "__main__":
    model = train_model(train_inputs, train_outputs, epochs=300,    model_save_path="rlc_model.pth")

    print("Performing inference on test set...")
    with torch.no_grad():
        X_test = torch.tensor(test_inputs[:, None, :], dtype=torch.float32)
        y_pred = model(X_test).squeeze().numpy()

    peak_pred = np.max(np.abs(y_pred), axis=1)
    peak_true = np.max(np.abs(test_outputs), axis=1)

    relative_peak_errors = np.abs(peak_pred - peak_true) / peak_true * 100

    print("Relative peak error (absolute) ratio for first 6 test samples:")
    for i in range(len(relative_peak_errors)):
        print(f"Sample {i + 1}: {relative_peak_errors[i]:.4f}%")

    #print("Displaying predictions...")
    #plot_predictions(test_inputs, test_outputs, y_pred, test_labels, num=6)

    #plot_convtranspose_weights(model)
    #plot_convtranspose_frequency_response(model)
